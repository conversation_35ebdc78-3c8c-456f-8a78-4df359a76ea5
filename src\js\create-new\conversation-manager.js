/**
 * Conversation Manager
 * Handles natural language interface, command processing, and conversational intelligence
 */

class ConversationManager {
    constructor(aiProvider, meenoeIntegration, agenticEngine) {
        this.aiProvider = aiProvider;
        this.meenoeIntegration = meenoeIntegration;
        this.agenticEngine = agenticEngine;

        this.conversationHistory = [];
        this.intentClassifier = new IntentClassifier();
        this.commandProcessor = new CommandProcessor(meenoeIntegration);
        this.contextManager = new ConversationContextManager();

        // Initialize the unified function calling system
        this.functionCaller = new UnifiedFunctionCaller(aiProvider, meenoeIntegration);

        this.systemPrompt = this.buildSystemPrompt();

        console.log('💬 Conversation Manager initialized with Unified Function Calling');
    }

    buildSystemPrompt() {
        return `You are an expert AI assistant for Meenoe, a meeting management platform. You help users create, manage, and optimize their meetings through natural conversation.

Your capabilities include:
- Creating and managing agenda items
- Creating and managing action items  
- Analyzing meeting structure and providing recommendations
- Executing complex workflows like meeting optimization
- Understanding context and providing proactive assistance

You have access to comprehensive Meenoe functions through function calling. Always use the appropriate functions to take actions rather than just describing what to do.

Key principles:
1. Be proactive and helpful
2. Understand context and user intent
3. Take concrete actions when requested
4. Provide clear explanations of what you're doing
5. Ask for clarification when needed
6. Suggest improvements and optimizations

Current session context will be provided with each message.`;
    }

    async processMessage(userMessage, context = {}) {
        try {
            console.log('💬 Processing message:', userMessage);

            // Add user message to history
            this.addToHistory('user', userMessage);

            // Enhanced context with current state
            const enhancedContext = {
                ...context,
                conversationHistory: this.getRecentHistory(),
                currentMeenoeState: this.meenoeIntegration.getCurrentMeenoeState(),
                agentContext: this.agenticEngine?.getContextSummary()
            };

            // Use the unified function calling system
            const result = await this.functionCaller.processRequest(userMessage, enhancedContext);

            // Combine message and summary for non-streaming responses
            let finalMessage = result.message;
            if (result.summary) {
                finalMessage += '\n\n' + result.summary;
            }

            // Add response to history
            this.addToHistory('assistant', finalMessage);

            return {
                ...result,
                message: finalMessage
            };

        } catch (error) {
            console.error('Error processing message:', error);
            const errorResponse = {
                message: "I apologize, but I encountered an error processing your request. Could you please try rephrasing or let me know what specific help you need?",
                type: 'error',
                error: error.message
            };

            this.addToHistory('assistant', errorResponse.message);
            return errorResponse;
        }
    }

    async processFunctionCallIntent(userMessage, intent, context) {
        const enhancedContext = {
            ...context,
            conversationHistory: this.getRecentHistory(),
            systemPrompt: this.systemPrompt,
            currentMeenoeState: this.meenoeIntegration.getCurrentMeenoeState(),
            availableFunctions: this.meenoeIntegration.getFunctionDefinitions()
        };

        try {
            // Use AI to determine which function to call
            const functionCall = await this.aiProvider.getFunctionCall(
                userMessage,
                enhancedContext.availableFunctions,
                enhancedContext
            );

            if (functionCall) {
                console.log('🔧 Executing function:', functionCall.name);
                
                // Execute the function
                const functionResult = await this.meenoeIntegration.executeFunction(
                    functionCall.name,
                    JSON.parse(functionCall.arguments)
                );

                // Generate response about the action taken
                const responseMessage = await this.generateActionResponse(
                    userMessage,
                    functionCall,
                    functionResult,
                    enhancedContext
                );

                return {
                    message: responseMessage,
                    type: 'function_call',
                    functionCall: functionCall,
                    functionResult: functionResult,
                    intent: intent
                };
            } else {
                // Fallback to conversational response
                return await this.processConversationalIntent(userMessage, intent, context);
            }
        } catch (error) {
            console.error('Error in function call processing:', error);
            
            // Try to provide helpful error response
            const errorResponse = await this.generateErrorResponse(userMessage, error, context);
            return {
                message: errorResponse,
                type: 'function_error',
                error: error.message,
                intent: intent
            };
        }
    }

    async processConversationalIntent(userMessage, intent, context) {
        const enhancedContext = {
            ...context,
            conversationHistory: this.getRecentHistory(),
            systemPrompt: this.systemPrompt,
            currentMeenoeState: this.meenoeIntegration.getCurrentMeenoeState(),
            agentContext: this.agenticEngine.getContextSummary()
        };

        const response = await this.aiProvider.generateResponse(
            userMessage,
            enhancedContext,
            {
                temperature: 0.7,
                maxTokens: 500
            }
        );

        return {
            message: response,
            type: 'conversational',
            intent: intent
        };
    }

    async generateActionResponse(userMessage, functionCall, functionResult, context) {
        const prompt = `The user said: "${userMessage}"

I executed the function: ${functionCall.name} with parameters: ${functionCall.arguments}

The result was: ${JSON.stringify(functionResult)}

Generate a natural, helpful response explaining what was accomplished and any next steps or suggestions.`;

        return await this.aiProvider.generateResponse(prompt, context, {
            temperature: 0.7,
            maxTokens: 300
        });
    }

    async generateErrorResponse(userMessage, error, context) {
        const prompt = `The user said: "${userMessage}"

An error occurred: ${error.message}

Generate a helpful response that:
1. Acknowledges the issue
2. Suggests alternative approaches
3. Asks for clarification if needed
4. Remains positive and helpful`;

        try {
            return await this.aiProvider.generateResponse(prompt, context, {
                temperature: 0.7,
                maxTokens: 200
            });
        } catch (aiError) {
            return "I encountered an issue processing your request. Could you please try rephrasing what you'd like me to help you with?";
        }
    }

    addToHistory(role, content) {
        this.conversationHistory.push({
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        });

        // Keep only recent history (last 20 messages)
        if (this.conversationHistory.length > 20) {
            this.conversationHistory = this.conversationHistory.slice(-20);
        }
    }

    getRecentHistory(count = 10) {
        return this.conversationHistory.slice(-count);
    }

    getFullHistory() {
        return [...this.conversationHistory];
    }

    clearHistory() {
        this.conversationHistory = [];
    }

    async streamResponse(userMessage, context = {}, onChunk) {
        try {
            console.log('🌊 Starting agentic stream response for:', userMessage);

            // Add user message to history
            this.addToHistory('user', userMessage);

            // Enhanced context
            const enhancedContext = {
                ...context,
                conversationHistory: this.getRecentHistory(),
                currentMeenoeState: this.meenoeIntegration.getCurrentMeenoeState(),
                agentContext: this.agenticEngine?.getContextSummary()
            };

            let fullResponse = '';

            // Progress callback for agentic execution
            const progressCallback = (progressMessage) => {
                onChunk(progressMessage + '\n\n');
                fullResponse += progressMessage + '\n\n';
            };

            // Use the unified function calling system with progress updates
            const result = await this.functionCaller.processRequest(
                userMessage,
                enhancedContext,
                progressCallback
            );

            // Stream the main response
            if (result.message) {
                const messageChunks = result.message.split(' ');
                for (const chunk of messageChunks) {
                    onChunk(chunk + ' ');
                    fullResponse += chunk + ' ';
                    // Small delay for streaming effect
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            }

            // Add execution summary if there were function calls
            if (result.summary) {
                onChunk('\n\n' + result.summary);
                fullResponse += '\n\n' + result.summary;
            }

            // Add to history
            this.addToHistory('assistant', fullResponse);

            return {
                ...result,
                message: fullResponse,
                type: 'agentic_stream'
            };

        } catch (error) {
            console.error('Error in agentic stream response:', error);
            const errorMessage = 'I encountered an error while processing your request. Please try again.';
            onChunk(errorMessage);

            this.addToHistory('assistant', errorMessage);
            return {
                message: errorMessage,
                type: 'stream_error',
                error: error.message
            };
        }
    }
}

/**
 * Intent Classifier
 * Determines user intent from natural language input
 */
class IntentClassifier {
    constructor() {
        this.intentPatterns = this.initializeIntentPatterns();
    }

    initializeIntentPatterns() {
        return {
            // Action-oriented intents (require function calls)
            create_agenda: {
                patterns: [
                    /create.*agenda/i,
                    /add.*agenda/i,
                    /new.*agenda/i,
                    /agenda.*item/i
                ],
                requiresFunctionCall: true,
                confidence: 0.8
            },
            
            create_action: {
                patterns: [
                    /create.*action/i,
                    /add.*action/i,
                    /new.*action/i,
                    /action.*item/i,
                    /todo/i,
                    /task/i
                ],
                requiresFunctionCall: true,
                confidence: 0.8
            },
            
            update_item: {
                patterns: [
                    /update/i,
                    /modify/i,
                    /change/i,
                    /edit/i
                ],
                requiresFunctionCall: true,
                confidence: 0.7
            },
            
            delete_item: {
                patterns: [
                    /delete/i,
                    /remove/i,
                    /get rid of/i
                ],
                requiresFunctionCall: true,
                confidence: 0.8
            },
            
            analyze_meeting: {
                patterns: [
                    /analyze/i,
                    /review/i,
                    /how.*doing/i,
                    /status/i,
                    /summary/i
                ],
                requiresFunctionCall: true,
                confidence: 0.6
            },
            
            optimize_meeting: {
                patterns: [
                    /optimize/i,
                    /improve/i,
                    /better/i,
                    /suggestions/i,
                    /recommendations/i
                ],
                requiresFunctionCall: true,
                confidence: 0.7
            },
            
            // Informational intents (conversational)
            greeting: {
                patterns: [
                    /^(hi|hello|hey)/i,
                    /good (morning|afternoon|evening)/i
                ],
                requiresFunctionCall: false,
                confidence: 0.9
            },
            
            help: {
                patterns: [
                    /help/i,
                    /what.*can.*do/i,
                    /how.*work/i
                ],
                requiresFunctionCall: false,
                confidence: 0.8
            },
            
            question: {
                patterns: [
                    /\?$/,
                    /^(what|how|why|when|where|who)/i
                ],
                requiresFunctionCall: false,
                confidence: 0.6
            }
        };
    }

    async classifyIntent(message, context = {}) {
        const normalizedMessage = message.toLowerCase().trim();
        
        let bestMatch = {
            intent: 'general',
            confidence: 0.3,
            requiresFunctionCall: false
        };

        // Check against patterns
        for (const [intentName, intentData] of Object.entries(this.intentPatterns)) {
            for (const pattern of intentData.patterns) {
                if (pattern.test(normalizedMessage)) {
                    if (intentData.confidence > bestMatch.confidence) {
                        bestMatch = {
                            intent: intentName,
                            confidence: intentData.confidence,
                            requiresFunctionCall: intentData.requiresFunctionCall
                        };
                    }
                }
            }
        }

        // Context-based adjustments
        bestMatch = this.adjustIntentWithContext(bestMatch, message, context);

        return bestMatch;
    }

    adjustIntentWithContext(intent, message, context) {
        // Boost confidence for function calls if context suggests action is needed
        if (intent.requiresFunctionCall) {
            const actionWords = ['please', 'can you', 'would you', 'i need', 'i want'];
            const hasActionWord = actionWords.some(word => 
                message.toLowerCase().includes(word)
            );
            
            if (hasActionWord) {
                intent.confidence = Math.min(0.95, intent.confidence + 0.1);
            }
        }

        // Adjust based on current state
        if (context.currentMeenoeState) {
            const state = context.currentMeenoeState;
            
            // If meeting is empty, boost create intents
            if (state.counters.agendaPoints === 0 && intent.intent === 'create_agenda') {
                intent.confidence = Math.min(0.95, intent.confidence + 0.15);
            }
            
            if (state.counters.actions === 0 && intent.intent === 'create_action') {
                intent.confidence = Math.min(0.95, intent.confidence + 0.15);
            }
        }

        return intent;
    }
}

/**
 * Command Processor
 * Processes direct commands and shortcuts
 */
class CommandProcessor {
    constructor(meenoeIntegration) {
        this.meenoeIntegration = meenoeIntegration;
        this.commands = this.initializeCommands();
    }

    initializeCommands() {
        return {
            '/agenda': {
                description: 'Create a new agenda item',
                handler: this.handleCreateAgenda.bind(this)
            },
            '/action': {
                description: 'Create a new action item',
                handler: this.handleCreateAction.bind(this)
            },
            '/status': {
                description: 'Show meeting status',
                handler: this.handleShowStatus.bind(this)
            },
            '/analyze': {
                description: 'Analyze meeting structure',
                handler: this.handleAnalyzeMeeting.bind(this)
            },
            '/help': {
                description: 'Show available commands',
                handler: this.handleShowHelp.bind(this)
            }
        };
    }

    isCommand(message) {
        return message.trim().startsWith('/');
    }

    async processCommand(message) {
        const parts = message.trim().split(' ');
        const command = parts[0].toLowerCase();
        const args = parts.slice(1).join(' ');

        const commandHandler = this.commands[command];
        if (commandHandler) {
            return await commandHandler.handler(args);
        } else {
            return {
                message: `Unknown command: ${command}. Type /help for available commands.`,
                type: 'command_error'
            };
        }
    }

    async handleCreateAgenda(args) {
        if (!args.trim()) {
            return {
                message: 'Please provide a title for the agenda item. Example: /agenda Budget Review',
                type: 'command_help'
            };
        }

        try {
            const result = await this.meenoeIntegration.createAgendaPoint(args.trim());
            return {
                message: `Created agenda item: "${args.trim()}"`,
                type: 'command_success',
                result: result
            };
        } catch (error) {
            return {
                message: `Error creating agenda item: ${error.message}`,
                type: 'command_error'
            };
        }
    }

    async handleCreateAction(args) {
        if (!args.trim()) {
            return {
                message: 'Please provide a title for the action item. Example: /action Review budget proposal',
                type: 'command_help'
            };
        }

        try {
            const result = await this.meenoeIntegration.createAction(args.trim());
            return {
                message: `Created action item: "${args.trim()}"`,
                type: 'command_success',
                result: result
            };
        } catch (error) {
            return {
                message: `Error creating action item: ${error.message}`,
                type: 'command_error'
            };
        }
    }

    async handleShowStatus() {
        try {
            const state = this.meenoeIntegration.getCurrentMeenoeState();
            const summary = this.meenoeIntegration.generateMeetingSummary();
            
            return {
                message: `Meeting Status:
• Title: ${state.name}
• Participants: ${state.counters.users}
• Agenda Items: ${state.counters.agendaPoints}
• Action Items: ${state.counters.actions}
• Files: ${state.counters.files}`,
                type: 'command_success',
                result: summary
            };
        } catch (error) {
            return {
                message: `Error getting status: ${error.message}`,
                type: 'command_error'
            };
        }
    }

    async handleAnalyzeMeeting() {
        try {
            const analysis = this.meenoeIntegration.analyzeMeetingStructure();
            
            return {
                message: `Meeting Analysis:
• Completeness: ${analysis.overall.completeness}%
• Balance: ${analysis.overall.balance}%
• Efficiency: ${analysis.overall.efficiency}%
• Recommendations: ${analysis.recommendations.length} items`,
                type: 'command_success',
                result: analysis
            };
        } catch (error) {
            return {
                message: `Error analyzing meeting: ${error.message}`,
                type: 'command_error'
            };
        }
    }

    handleShowHelp() {
        const helpText = Object.entries(this.commands)
            .map(([cmd, info]) => `${cmd} - ${info.description}`)
            .join('\n');

        return {
            message: `Available commands:\n${helpText}`,
            type: 'command_help'
        };
    }
}

/**
 * Conversation Context Manager
 * Manages conversation context and references
 */
class ConversationContextManager {
    constructor() {
        this.contextStack = [];
        this.references = new Map();
    }

    pushContext(context) {
        this.contextStack.push(context);
        
        // Keep only recent context
        if (this.contextStack.length > 10) {
            this.contextStack = this.contextStack.slice(-10);
        }
    }

    getCurrentContext() {
        return this.contextStack[this.contextStack.length - 1] || {};
    }

    resolveReference(reference) {
        return this.references.get(reference.toLowerCase());
    }

    addReference(key, value) {
        this.references.set(key.toLowerCase(), value);
    }
}

// Export classes
if (typeof window !== 'undefined') {
    window.ConversationManager = ConversationManager;
    window.IntentClassifier = IntentClassifier;
    window.CommandProcessor = CommandProcessor;
    window.ConversationContextManager = ConversationContextManager;
}
