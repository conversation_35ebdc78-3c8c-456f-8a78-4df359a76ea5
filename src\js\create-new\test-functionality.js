/**
 * Test Functionality for Meenoe Details and At-A-Glance Cards
 * This script provides testing functions to verify the new functionality
 */

window.meenoeTestSuite = {
    
    /**
     * Test the state management system
     */
    testStateManagement() {
        console.log('🧪 Testing State Management...');
        
        if (!window.meenoeState) {
            console.error('❌ meenoeState not found');
            return false;
        }
        
        // Test getting current state
        const state = window.meenoeState.getState();
        console.log('✅ Current state:', state);
        
        // Test counter updates
        window.meenoeState.updateUserCount({ selectedUsers: [{id: 1}, {id: 2}], invitedGuests: [] });
        console.log('✅ User count updated');
        
        return true;
    },
    
    /**
     * Test the details editing functionality
     */
    testDetailsEditing() {
        console.log('🧪 Testing Details Editing...');

        if (!window.meenoeDetails) {
            console.error('❌ meenoeDetails not found');
            return false;
        }

        // Test name editing
        const nameElement = document.getElementById('meenoe-name');
        const editButton = document.getElementById('edit-meenoe-name');

        if (nameElement && editButton) {
            console.log('✅ Name editing elements found');
            // Simulate click
            editButton.click();
            console.log('✅ Edit button clicked');
        } else {
            console.error('❌ Name editing elements not found');
            return false;
        }

        return true;
    },

    /**
     * Test real-time name syncing between meenoe-name and meenoe-name2
     */
    testNameSyncing() {
        console.log('🧪 Testing Real-Time Name Syncing...');

        const nameElement = document.getElementById('meenoe-name');
        const headerContainer = document.getElementById('meenoe-name2');
        const headerInput = headerContainer?.querySelector('input');

        if (!nameElement || !headerInput) {
            console.error('❌ Name elements not found');
            return false;
        }

        console.log('✅ Both name elements found');

        // Test syncing from header input to details name
        const testName1 = 'Test Meeting ' + Date.now();
        headerInput.value = testName1;
        headerInput.dispatchEvent(new Event('input', { bubbles: true }));

        setTimeout(() => {
            if (nameElement.textContent === testName1) {
                console.log('✅ Header to details sync working');
            } else {
                console.error('❌ Header to details sync failed');
                console.log('Expected:', testName1, 'Got:', nameElement.textContent);
            }
        }, 100);

        // Test syncing from details name to header input
        setTimeout(() => {
            const testName2 = 'Another Test ' + Date.now();
            nameElement.contentEditable = true;
            nameElement.textContent = testName2;
            nameElement.dispatchEvent(new Event('input', { bubbles: true }));

            setTimeout(() => {
                if (headerInput.value === testName2) {
                    console.log('✅ Details to header sync working');
                } else {
                    console.error('❌ Details to header sync failed');
                    console.log('Expected:', testName2, 'Got:', headerInput.value);
                }
                nameElement.contentEditable = false;
            }, 100);
        }, 200);

        return true;
    },
    
    /**
     * Test the at-a-glance cards
     */
    testAtAGlanceCards() {
        console.log('🧪 Testing At-A-Glance Cards...');
        
        if (!window.atAGlanceCards) {
            console.error('❌ atAGlanceCards not found');
            return false;
        }
        
        // Test card elements
        const cards = ['users', 'agenda', 'files', 'actions'];
        let allFound = true;
        
        cards.forEach(cardType => {
            const element = document.getElementById(`${cardType}-count`);
            if (element) {
                console.log(`✅ ${cardType} card found: ${element.textContent}`);
            } else {
                console.error(`❌ ${cardType} card not found`);
                allFound = false;
            }
        });
        
        return allFound;
    },
    
    /**
     * Test integrations with existing systems
     */
    testIntegrations() {
        console.log('🧪 Testing Integrations...');
        
        if (!window.meenoeIntegrations) {
            console.error('❌ meenoeIntegrations not found');
            return false;
        }
        
        // Test sync
        window.meenoeIntegrations.syncNow();
        console.log('✅ Manual sync triggered');
        
        return true;
    },
    
    /**
     * Test adding users and see if counter updates
     */
    testUserCountUpdate() {
        console.log('🧪 Testing User Count Update...');
        
        // Simulate user addition
        if (window.meenoeUsers) {
            const originalCount = window.meenoeUsers.getSelectedPeople().length;
            console.log(`📊 Original user count: ${originalCount}`);
            
            // Add a test user
            window.meenoeUsers.selectedUsers.push({
                id: 'test-user-' + Date.now(),
                name: 'Test User',
                email: '<EMAIL>'
            });
            
            // Notify change
            window.meenoeUsers.notifyUserChange();
            
            setTimeout(() => {
                const newCount = window.meenoeUsers.getSelectedPeople().length;
                console.log(`📊 New user count: ${newCount}`);
                
                const counterElement = document.getElementById('users-count');
                if (counterElement) {
                    console.log(`📊 Counter display: ${counterElement.textContent}`);
                }
            }, 500);
            
            return true;
        }
        
        console.error('❌ meenoeUsers not found');
        return false;
    },
    
    /**
     * Test adding agenda point and see if counter updates
     */
    testAgendaCountUpdate() {
        console.log('🧪 Testing Agenda Count Update...');
        
        if (window.agendaFlow) {
            const originalCount = window.agendaFlow.state.agendaItems.size;
            console.log(`📊 Original agenda count: ${originalCount}`);
            
            // Add a test agenda point
            window.agendaFlow.addNewAgendaPoint('Test Agenda Point', 'This is a test agenda point');
            
            setTimeout(() => {
                const newCount = window.agendaFlow.state.agendaItems.size;
                console.log(`📊 New agenda count: ${newCount}`);
                
                const counterElement = document.getElementById('agenda-count');
                if (counterElement) {
                    console.log(`📊 Counter display: ${counterElement.textContent}`);
                }
            }, 500);
            
            return true;
        }
        
        console.error('❌ agendaFlow not found');
        return false;
    },
    
    /**
     * Test adding action and see if counter updates
     */
    testActionCountUpdate() {
        console.log('🧪 Testing Action Count Update...');
        
        if (window.tree) {
            const originalCount = window.meenoeState ? window.meenoeState.countActionsInTree() : 0;
            console.log(`📊 Original action count: ${originalCount}`);
            
            // Add a test action
            window.tree.createNode('Test Action', false, null, null, null, 'context1');
            
            setTimeout(() => {
                const newCount = window.meenoeState ? window.meenoeState.countActionsInTree() : 0;
                console.log(`📊 New action count: ${newCount}`);
                
                const counterElement = document.getElementById('actions-count');
                if (counterElement) {
                    console.log(`📊 Counter display: ${counterElement.textContent}`);
                }
            }, 500);
            
            return true;
        }
        
        console.error('❌ tree not found');
        return false;
    },
    
    /**
     * Run all tests
     */
    runAllTests() {
        console.log('🚀 Running Meenoe Test Suite...');
        console.log('=====================================');
        
        const tests = [
            this.testStateManagement,
            this.testDetailsEditing,
            this.testNameSyncing,
            this.testAtAGlanceCards,
            this.testIntegrations
        ];
        
        let passed = 0;
        let total = tests.length;
        
        tests.forEach((test, index) => {
            try {
                if (test.call(this)) {
                    passed++;
                }
            } catch (error) {
                console.error(`❌ Test ${index + 1} failed:`, error);
            }
        });
        
        console.log('=====================================');
        console.log(`📊 Test Results: ${passed}/${total} tests passed`);
        
        if (passed === total) {
            console.log('🎉 All tests passed!');
        } else {
            console.log('⚠️ Some tests failed. Check the logs above.');
        }
        
        return passed === total;
    },
    
    /**
     * Run interactive tests (with actual UI changes)
     */
    runInteractiveTests() {
        console.log('🎮 Running Interactive Tests...');
        console.log('=====================================');
        
        // Test user count update
        setTimeout(() => this.testUserCountUpdate(), 1000);
        
        // Test agenda count update
        setTimeout(() => this.testAgendaCountUpdate(), 2000);
        
        // Test action count update
        setTimeout(() => this.testActionCountUpdate(), 3000);
        
        console.log('⏱️ Interactive tests scheduled. Watch the counters!');
    }
};

// Auto-run basic tests when script loads
setTimeout(() => {
    if (window.meenoeTestSuite) {
        console.log('🔧 Meenoe Test Suite loaded. Run window.meenoeTestSuite.runAllTests() to test functionality.');
        console.log('🔧 Run window.meenoeTestSuite.runInteractiveTests() to test counter updates.');
    }
}, 1000);
