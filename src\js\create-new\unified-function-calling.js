/**
 * Unified Agentic System
 * Parses AI responses for function calls and executes them
 * Provides real-time progress updates and UI changes
 */

class UnifiedFunctionCaller {
    constructor(aiProvider, meenoeIntegration) {
        this.aiProvider = aiProvider;
        this.meenoeIntegration = meenoeIntegration;
        this.functionRegistry = new Map();
        this.intentPatterns = new Map();

        // Execution tracking
        this.currentExecution = null;
        this.executionCallbacks = [];

        this.initializeFunctionRegistry();
        this.initializeIntentPatterns();

        console.log('🔧 Unified Agentic System initialized');
    }

    initializeFunctionRegistry() {
        // Get all available functions from Meenoe integration
        const functions = this.meenoeIntegration.getFunctionDefinitions();
        
        functions.forEach(func => {
            this.functionRegistry.set(func.name, {
                ...func,
                handler: this.meenoeIntegration.executeFunction.bind(this.meenoeIntegration)
            });
        });

        console.log(`📋 Registered ${this.functionRegistry.size} functions:`, 
            Array.from(this.functionRegistry.keys()));
    }

    initializeIntentPatterns() {
        // Enhanced intent patterns with better matching
        this.intentPatterns.set('create_agenda', {
            patterns: [
                /create.*agenda.*point/i,
                /add.*agenda.*item/i,
                /new.*agenda/i,
                /agenda.*point.*named/i,
                /agenda.*item.*called/i,
                /make.*agenda/i
            ],
            function: 'createAgendaPoint',
            extractors: {
                title: [
                    /agenda.*point.*named\s+"([^"]+)"/i,
                    /agenda.*item.*called\s+"([^"]+)"/i,
                    /create.*agenda.*point\s+"([^"]+)"/i,
                    /add.*agenda.*item\s+"([^"]+)"/i,
                    /new.*agenda.*point\s+"([^"]+)"/i,
                    /"([^"]+)"/i // fallback for any quoted text
                ],
                description: [
                    /description[:\s]+"([^"]+)"/i,
                    /about\s+"([^"]+)"/i,
                    /regarding\s+"([^"]+)"/i
                ],
                urgency: [
                    /(normal|moderate|important|critical|mandatory)\s+priority/i,
                    /(normal|moderate|important|critical|mandatory)\s+urgency/i,
                    /priority[:\s]+(normal|moderate|important|critical|mandatory)/i,
                    /urgency[:\s]+(normal|moderate|important|critical|mandatory)/i
                ]
            }
        });

        this.intentPatterns.set('create_action', {
            patterns: [
                /create.*action.*item/i,
                /add.*action/i,
                /new.*action/i,
                /action.*item.*named/i,
                /task.*called/i,
                /todo.*item/i,
                /assign.*task/i
            ],
            function: 'createAction',
            extractors: {
                title: [
                    /action.*item.*named\s+"([^"]+)"/i,
                    /task.*called\s+"([^"]+)"/i,
                    /create.*action\s+"([^"]+)"/i,
                    /add.*action\s+"([^"]+)"/i,
                    /todo.*item\s+"([^"]+)"/i,
                    /"([^"]+)"/i
                ],
                description: [
                    /description[:\s]+"([^"]+)"/i,
                    /details[:\s]+"([^"]+)"/i
                ],
                assignee: [
                    /assign.*to\s+([^\s,]+)/i,
                    /assigned.*to\s+([^\s,]+)/i,
                    /for\s+([^\s,]+)/i,
                    /assignee[:\s]+([^\s,]+)/i
                ],
                priority: [
                    /(low|medium|high|critical)\s+priority/i,
                    /priority[:\s]+(low|medium|high|critical)/i
                ],
                dueDate: [
                    /due\s+(\d{4}-\d{2}-\d{2})/i,
                    /deadline[:\s]+(\d{4}-\d{2}-\d{2})/i,
                    /by\s+(\d{4}-\d{2}-\d{2})/i
                ]
            }
        });

        this.intentPatterns.set('update_item', {
            patterns: [
                /update.*agenda/i,
                /modify.*action/i,
                /change.*item/i,
                /edit.*point/i
            ],
            function: 'updateAgendaPoint', // Will be determined dynamically
            extractors: {
                id: [/item\s+(\w+)/i, /id[:\s]+(\w+)/i],
                title: [/title[:\s]+"([^"]+)"/i, /name[:\s]+"([^"]+)"/i],
                description: [/description[:\s]+"([^"]+)"/i]
            }
        });

        this.intentPatterns.set('delete_item', {
            patterns: [
                /delete.*agenda/i,
                /remove.*action/i,
                /get rid of.*item/i
            ],
            function: 'deleteAgendaPoint', // Will be determined dynamically
            extractors: {
                id: [/item\s+(\w+)/i, /id[:\s]+(\w+)/i],
                title: [/"([^"]+)"/i]
            }
        });
    }

    async processRequest(userMessage, context = {}, progressCallback = null) {
        console.log('🎯 Processing agentic request:', userMessage);

        try {
            // Step 1: Generate AI response with function calling instructions
            const aiResponse = await this.generateAgenticResponse(userMessage, context, progressCallback);

            // Step 2: Parse the response for function calls
            const functionCalls = this.parseResponseForFunctionCalls(aiResponse, context);

            // Step 3: Execute function calls if found (limit to 1)
            if (functionCalls.length > 0) {
                // Only execute the first function call to avoid multiple actions
                const singleFunctionCall = [functionCalls[0]];
                if (functionCalls.length > 1) {
                    console.warn(`⚠️ AI tried to call ${functionCalls.length} functions, executing only the first one`);
                }
                return await this.executeAgenticWorkflow(singleFunctionCall, aiResponse, progressCallback);
            }

            // Step 4: Return conversational response if no function calls
            return {
                success: true,
                type: 'conversational',
                message: aiResponse,
                functionCalls: []
            };

        } catch (error) {
            console.error('❌ Error in agentic processing:', error);
            return {
                success: false,
                type: 'error',
                message: `I encountered an error: ${error.message}`,
                error: error
            };
        }
    }

    async generateAgenticResponse(userMessage, context, progressCallback) {
        if (progressCallback) {
            progressCallback('🤔 Analyzing your request...');
        }

        const availableFunctions = Array.from(this.functionRegistry.values());

        // Get current state for context
        const currentState = context.currentMeenoeState || {};
        const agendaItems = this.extractAgendaItems(currentState);
        const actionItems = this.extractActionItems(currentState);

        const prompt = `You are an agentic AI assistant for Meenoe meeting management. You can perform actions by calling functions.

User Request: "${userMessage}"

CURRENT MEETING STATE:
Agenda Items:
${agendaItems.map(item => `- ID: ${item.id}, Title: "${item.title}", Urgency: ${item.urgency}`).join('\n') || '(No agenda items)'}

Action Items:
${actionItems.map(item => `- ID: ${item.id}, Title: "${item.title}", Status: ${item.status}`).join('\n') || '(No action items)'}

Available Functions:
${availableFunctions.map(func =>
    `- ${func.name}: ${func.description}\n  Parameters: ${JSON.stringify(func.parameters.properties, null, 2)}`
).join('\n\n')}

IMPORTANT RULES:
1. When referencing existing items, use the EXACT IDs from the current state above.
2. When user says "the [item name]" or "that [item]", find the matching ID from the current state.
3. For agenda items, use IDs like "agenda-1751000594103" (from current state).
4. For action items, use IDs like "action_xug8tv40c" (from current state).
5. NEVER make up IDs like "agenda-123" or "action-1".

Instructions:
1. If the user's request requires performing actions, include function calls in your response using this EXACT format:
   FUNCTION_CALL: {"function": "functionName", "parameters": {"param1": "value1"}}

2. IMPORTANT: Always ensure JSON is properly formatted with matching braces and quotes.

3. CRITICAL: Only call ONE function per request. Do not call getCurrentMeenoeState or multiple functions.

4. For agenda items, use "createAgendaPoint" function with parameters: title (required), description (optional), urgency (optional: normal/moderate/important/critical/mandatory). DEFAULT urgency is "normal".

5. For action items, use "createAction" function with parameters: title (required), description (optional), assignee (optional), priority (optional), dueDate (optional).

6. When adding threads to agenda items, use "addThreadToAgenda" with the EXACT agendaId from the current state.

7. When user refers to items by name (e.g., "the sales retreat planning agenda"), find the matching ID from the current state above.

8. Provide a brief response explaining what you're doing.

Example response format:
I'll create that agenda point for you.

FUNCTION_CALL: {"function": "createAgendaPoint", "parameters": {"title": "Budget Review", "urgency": "high"}}

The agenda point has been created successfully.

Now respond to the user's request:`;

        try {
            const response = await this.aiProvider.generateResponse(prompt, context, {
                temperature: 0.3,
                maxTokens: 800
            });

            return response;
        } catch (error) {
            console.error('AI response generation failed:', error);
            throw error;
        }
    }

    parseResponseForFunctionCalls(response, context) {
        const functionCalls = [];

        // Look for FUNCTION_CALL: patterns with more flexible matching
        const functionCallPattern = /FUNCTION_CALL:\s*(\{[\s\S]*?)(?=\n\n|\nFUNCTION_CALL:|$)/g;
        let match;

        while ((match = functionCallPattern.exec(response)) !== null) {
            let jsonStr = match[1].trim();

            // Try to fix common JSON issues
            jsonStr = this.fixMalformedJson(jsonStr);

            try {
                const functionCall = JSON.parse(jsonStr);
                if (functionCall.function && functionCall.parameters !== undefined) {
                    // Resolve IDs before adding to the list
                    this.resolveParameterIDs(functionCall, response, context);
                    functionCalls.push(functionCall);
                    console.log('🔧 Parsed function call:', functionCall);
                }
            } catch (error) {
                console.error('Failed to parse function call:', jsonStr, error);

                // Try to extract function and parameters manually
                const manualParse = this.manuallyParseFunctionCall(jsonStr);
                if (manualParse) {
                    functionCalls.push(manualParse);
                    console.log('🔧 Manually parsed function call:', manualParse);
                }
            }
        }

        return functionCalls;
    }

    fixMalformedJson(jsonStr) {
        // Remove any trailing text after the JSON
        let cleaned = jsonStr;

        // Find the last complete brace
        let braceCount = 0;
        let lastValidIndex = -1;

        for (let i = 0; i < cleaned.length; i++) {
            if (cleaned[i] === '{') {
                braceCount++;
            } else if (cleaned[i] === '}') {
                braceCount--;
                if (braceCount === 0) {
                    lastValidIndex = i;
                    break;
                }
            }
        }

        if (lastValidIndex > -1) {
            cleaned = cleaned.substring(0, lastValidIndex + 1);
        } else if (braceCount > 0) {
            // Add missing closing braces
            cleaned += '}';
        }

        return cleaned;
    }

    manuallyParseFunctionCall(jsonStr) {
        try {
            // Extract function name
            const functionMatch = jsonStr.match(/"function":\s*"([^"]+)"/);
            if (!functionMatch) return null;

            const functionName = functionMatch[1];

            // Extract parameters object
            const parametersMatch = jsonStr.match(/"parameters":\s*(\{[^}]*\}?)/);
            let parameters = {};

            if (parametersMatch) {
                try {
                    // Try to parse parameters
                    let paramStr = parametersMatch[1];
                    if (!paramStr.endsWith('}')) {
                        paramStr += '}';
                    }
                    parameters = JSON.parse(paramStr);
                } catch (e) {
                    // Extract individual parameter values
                    const titleMatch = jsonStr.match(/"title":\s*"([^"]+)"/);
                    const descriptionMatch = jsonStr.match(/"description":\s*"([^"]*)"/);
                    const urgencyMatch = jsonStr.match(/"urgency":\s*"([^"]+)"/);
                    const assigneeMatch = jsonStr.match(/"assignee":\s*"([^"]+)"/);
                    const priorityMatch = jsonStr.match(/"priority":\s*"([^"]+)"/);

                    if (titleMatch) parameters.title = titleMatch[1];
                    if (descriptionMatch) parameters.description = descriptionMatch[1];
                    if (urgencyMatch) parameters.urgency = urgencyMatch[1];
                    if (assigneeMatch) parameters.assignee = assigneeMatch[1];
                    if (priorityMatch) parameters.priority = priorityMatch[1];
                }
            }

            return {
                function: functionName,
                parameters: parameters
            };

        } catch (error) {
            console.error('Manual parsing failed:', error);
            return null;
        }
    }

    async executeAgenticWorkflow(functionCalls, originalResponse, progressCallback) {
        const results = [];
        let cleanResponse = originalResponse;

        // Remove function calls from the response text
        cleanResponse = cleanResponse.replace(/FUNCTION_CALL:\s*\{[^}]+\}/g, '').trim();

        if (progressCallback) {
            progressCallback(`🔧 Executing ${functionCalls.length} action(s)...`);
        }

        // Execute each function call
        for (let i = 0; i < functionCalls.length; i++) {
            const functionCall = functionCalls[i];

            if (progressCallback) {
                progressCallback(`⚙️ Step ${i + 1}/${functionCalls.length}: ${functionCall.function}...`);
            }

            try {
                console.log(`🔧 Executing: ${functionCall.function}`, functionCall.parameters);

                // Validate function exists
                if (!this.functionRegistry.has(functionCall.function)) {
                    throw new Error(`Function ${functionCall.function} not found in registry`);
                }

                const result = await this.meenoeIntegration.executeFunction(
                    functionCall.function,
                    functionCall.parameters
                );

                console.log(`✅ Function executed successfully:`, result);

                results.push({
                    function: functionCall.function,
                    parameters: functionCall.parameters,
                    result: result,
                    success: true
                });

                // Trigger UI updates
                this.triggerUIUpdates(functionCall.function, result);

            } catch (error) {
                console.error(`❌ Function execution failed: ${functionCall.function}`, error);
                results.push({
                    function: functionCall.function,
                    parameters: functionCall.parameters,
                    error: error.message,
                    success: false
                });
            }
        }

        if (progressCallback) {
            progressCallback('✅ Actions completed!');
        }

        // Generate final summary
        const summary = this.generateExecutionSummary(results);

        return {
            success: true,
            type: 'agentic_execution',
            message: cleanResponse,
            summary: summary,
            functionCalls: results,
            executedActions: results.filter(r => r.success).length
        };
    }

    triggerUIUpdates(functionName, result) {
        // Trigger appropriate UI updates based on the function executed
        switch (functionName) {
            case 'createAgendaPoint':
                // Trigger agenda refresh
                if (window.agendaFlow) {
                    window.dispatchEvent(new CustomEvent('meenoeAgendaChanged', {
                        detail: { action: 'added', agendaId: result.agendaId }
                    }));
                }
                break;

            case 'createAction':
                // Trigger actions refresh
                if (window.tree) {
                    window.dispatchEvent(new CustomEvent('meenoeActionsChanged', {
                        detail: { action: 'added', actionId: result.actionId }
                    }));
                }
                break;

            case 'updateUserCount':
                // Trigger counter refresh
                if (window.meenoeState) {
                    window.meenoeState.refreshAllCounters();
                }
                break;
        }
    }

    generateExecutionSummary(results) {
        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        let summary = '';

        if (successful.length > 0) {
            summary += `✅ Successfully completed ${successful.length} action(s):\n`;
            successful.forEach(result => {
                summary += `• ${this.getFunctionDisplayName(result.function)}\n`;
            });
        }

        if (failed.length > 0) {
            summary += `\n❌ ${failed.length} action(s) failed:\n`;
            failed.forEach(result => {
                summary += `• ${this.getFunctionDisplayName(result.function)}: ${result.error}\n`;
            });
        }

        return summary;
    }

    getFunctionDisplayName(functionName) {
        const displayNames = {
            'createAgendaPoint': 'Created agenda point',
            'createAction': 'Created action item',
            'updateUserCount': 'Updated participant count',
            'deleteAgendaPoint': 'Deleted agenda point',
            'updateAgendaPoint': 'Updated agenda point',
            'addThreadToAgenda': 'Added thread to agenda'
        };

        return displayNames[functionName] || functionName;
    }

    extractAgendaItems(state) {
        const items = [];

        // Check if agendaFlow state exists
        if (state.agendaFlow && state.agendaFlow.agendaItems) {
            if (state.agendaFlow.agendaItems instanceof Map) {
                // Handle Map structure
                for (const [id, item] of state.agendaFlow.agendaItems) {
                    items.push({
                        id: id,
                        title: item.title || 'Untitled',
                        urgency: item.urgency || 'important',
                        description: item.description || ''
                    });
                }
            } else if (Array.isArray(state.agendaFlow.agendaItems)) {
                // Handle Array structure
                state.agendaFlow.agendaItems.forEach(item => {
                    items.push({
                        id: item.id,
                        title: item.title || 'Untitled',
                        urgency: item.urgency || 'important',
                        description: item.description || ''
                    });
                });
            }
        }

        return items;
    }

    extractActionItems(state) {
        const items = [];

        // Check if actions tree exists
        if (state.actions && state.actions.childNodes) {
            const extractFromNodes = (nodes) => {
                nodes.forEach(node => {
                    items.push({
                        id: node.id,
                        title: node.actionTitle || 'Untitled',
                        status: node.actionStatus || 'open',
                        description: node.actionDescription || ''
                    });

                    // Recursively extract from child nodes
                    if (node.childNodes && node.childNodes.length > 0) {
                        extractFromNodes(node.childNodes);
                    }
                });
            };

            extractFromNodes(state.actions.childNodes);
        }

        return items;
    }

    resolveParameterIDs(functionCall, originalResponse, context) {
        // Get current state from context or fresh
        const currentState = context?.currentMeenoeState || this.meenoeIntegration.getCurrentMeenoeState();
        const agendaItems = this.extractAgendaItems(currentState);
        const actionItems = this.extractActionItems(currentState);

        console.log('🔍 Resolving IDs with current items:', { agendaItems, actionItems });

        // Resolve agenda IDs
        if (functionCall.parameters.agendaId) {
            const resolvedId = this.findBestMatchingId(
                functionCall.parameters.agendaId,
                agendaItems,
                originalResponse,
                'agenda'
            );
            if (resolvedId) {
                console.log(`🔍 Resolved agenda ID: ${functionCall.parameters.agendaId} → ${resolvedId}`);
                functionCall.parameters.agendaId = resolvedId;
            }
        }

        // Resolve action IDs
        if (functionCall.parameters.actionId) {
            const resolvedId = this.findBestMatchingId(
                functionCall.parameters.actionId,
                actionItems,
                originalResponse,
                'action'
            );
            if (resolvedId) {
                console.log(`🔍 Resolved action ID: ${functionCall.parameters.actionId} → ${resolvedId}`);
                functionCall.parameters.actionId = resolvedId;
            }
        }
    }

    findBestMatchingId(providedId, items, originalResponse, type) {
        // If the provided ID exists, use it
        const exactMatch = items.find(item => item.id === providedId);
        if (exactMatch) {
            return providedId;
        }

        // Try to find by title mentioned in the response or user request
        const titleMatches = this.extractTitleFromResponse(originalResponse, type);
        console.log(`🔍 Looking for ${type} titles:`, titleMatches);

        if (titleMatches.length > 0) {
            for (const titleMatch of titleMatches) {
                const item = items.find(item => {
                    const itemTitle = item.title.toLowerCase();
                    const searchTitle = titleMatch.toLowerCase();

                    // Exact match
                    if (itemTitle === searchTitle) return true;

                    // Contains match (both directions)
                    if (itemTitle.includes(searchTitle) || searchTitle.includes(itemTitle)) return true;

                    // Word-based matching for partial matches
                    const itemWords = itemTitle.split(/\s+/);
                    const searchWords = searchTitle.split(/\s+/);
                    const commonWords = itemWords.filter(word =>
                        searchWords.some(searchWord =>
                            word.includes(searchWord) || searchWord.includes(word)
                        )
                    );

                    // If more than half the words match, consider it a match
                    return commonWords.length >= Math.min(itemWords.length, searchWords.length) / 2;
                });

                if (item) {
                    console.log(`🎯 Found item by title match: "${titleMatch}" → ${item.id} (${item.title})`);
                    return item.id;
                }
            }
        }

        // If only one item exists, use it (likely the most recent)
        if (items.length === 1) {
            console.log(`🎯 Using only available ${type}: ${items[0].id}`);
            return items[0].id;
        }

        // Use the most recently created item (highest timestamp in ID)
        if (items.length > 0) {
            const mostRecent = items.reduce((latest, current) => {
                // Extract timestamp from ID (agenda-1751000594103 or action_xug8tv40c)
                const latestTime = this.extractTimestampFromId(latest.id);
                const currentTime = this.extractTimestampFromId(current.id);
                return currentTime > latestTime ? current : latest;
            });
            console.log(`🎯 Using most recent ${type}: ${mostRecent.id}`);
            return mostRecent.id;
        }

        return null;
    }

    extractTitleFromResponse(response, type) {
        const titles = [];

        // Look for quoted titles
        const quotedMatches = response.match(/"([^"]+)"/g);
        if (quotedMatches) {
            titles.push(...quotedMatches.map(match => match.replace(/"/g, '')));
        }

        // Look for specific patterns based on type
        if (type === 'agenda') {
            const agendaPatterns = [
                /agenda.*?(?:point|item).*?(?:called|named)\s+"?([^".!?]+)"?/gi,
                /(?:the|that)\s+"?([^".!?]+)"?\s+agenda/gi,
                /agenda.*?(?:point|item)\s+"?([^".!?]+)"?/gi,
                /(?:called|named)\s+"?([^".!?]+)"?/gi,
                /with.*?name\s+"?([^".!?]+)"?/gi
            ];

            agendaPatterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(response)) !== null) {
                    const title = match[1].trim();
                    if (title.length > 2) { // Avoid very short matches
                        titles.push(title);
                    }
                }
            });
        }

        // Remove duplicates and clean up
        const uniqueTitles = [...new Set(titles)].map(title =>
            title.replace(/^(the|that|a|an)\s+/i, '').trim()
        ).filter(title => title.length > 2);

        console.log(`🔍 Extracted ${type} titles from response:`, uniqueTitles);
        return uniqueTitles;
    }

    extractTimestampFromId(id) {
        // Extract timestamp from agenda-1751000594103 format
        const timestampMatch = id.match(/(\d{13})/);
        if (timestampMatch) {
            return parseInt(timestampMatch[1]);
        }

        // For action IDs without timestamps, use 0
        return 0;
    }

    tryPatternMatching(userMessage) {
        for (const [intentName, intentConfig] of this.intentPatterns) {
            // Check if any pattern matches
            const matchedPattern = intentConfig.patterns.find(pattern => pattern.test(userMessage));
            
            if (matchedPattern) {
                console.log(`🎯 Pattern matched for intent: ${intentName}`);
                
                // Extract parameters using extractors
                const parameters = {};
                
                for (const [paramName, extractors] of Object.entries(intentConfig.extractors)) {
                    for (const extractor of extractors) {
                        const match = userMessage.match(extractor);
                        if (match && match[1]) {
                            parameters[paramName] = match[1].trim();
                            break; // Use first successful extraction
                        }
                    }
                }

                // Apply intelligent defaults and validation
                this.applyIntelligentDefaults(intentName, parameters, userMessage);

                return {
                    intent: intentName,
                    function: intentConfig.function,
                    parameters: parameters,
                    confidence: 0.9
                };
            }
        }

        return null;
    }

    applyIntelligentDefaults(intent, parameters, userMessage) {
        switch (intent) {
            case 'create_agenda':
                // If no title extracted, try to infer from the message
                if (!parameters.title) {
                    // Remove command words and extract the main content
                    let inferredTitle = userMessage
                        .replace(/create|add|new|agenda|point|item/gi, '')
                        .replace(/named|called/gi, '')
                        .replace(/['"]/g, '')
                        .trim();
                    
                    if (inferredTitle.length > 3) {
                        parameters.title = inferredTitle;
                    } else {
                        parameters.title = 'New Agenda Point';
                    }
                }
                
                // Default urgency
                if (!parameters.urgency) {
                    parameters.urgency = 'normal';
                }
                break;

            case 'create_action':
                // Similar logic for actions
                if (!parameters.title) {
                    let inferredTitle = userMessage
                        .replace(/create|add|new|action|item|task|todo/gi, '')
                        .replace(/named|called/gi, '')
                        .replace(/['"]/g, '')
                        .trim();
                    
                    if (inferredTitle.length > 3) {
                        parameters.title = inferredTitle;
                    } else {
                        parameters.title = 'New Action Item';
                    }
                }
                
                // Default priority
                if (!parameters.priority) {
                    parameters.priority = 'medium';
                }
                break;
        }
    }

    async useAIForFunctionCalling(userMessage, context) {
        // Create a comprehensive prompt for function calling
        const availableFunctions = Array.from(this.functionRegistry.values());
        
        const prompt = `You are a function calling assistant for Meenoe. Analyze the user's request and determine if it requires calling a function.

User Request: "${userMessage}"

Available Functions:
${availableFunctions.map(func => 
    `- ${func.name}: ${func.description}\n  Parameters: ${JSON.stringify(func.parameters, null, 2)}`
).join('\n\n')}

Current Context:
${JSON.stringify(context, null, 2)}

If the user's request requires calling a function, respond with EXACTLY this JSON format:
{
    "function_call": true,
    "function": "function_name",
    "parameters": {
        "param1": "value1",
        "param2": "value2"
    },
    "reasoning": "Why this function was chosen"
}

If no function call is needed, respond with:
{
    "function_call": false,
    "reasoning": "Why no function call is needed"
}

Be intelligent about extracting parameters from natural language. For example:
- "Create agenda point called Budget Review" → {"title": "Budget Review"}
- "Add action for John to review documents by Friday" → {"title": "review documents", "assignee": "John", "dueDate": "2024-01-05"}`;

        try {
            const response = await this.aiProvider.generateResponse(prompt, context, {
                temperature: 0.1, // Low temperature for consistent function calling
                maxTokens: 500
            });

            // Parse the AI response
            const parsed = this.parseAIResponse(response);
            return parsed;

        } catch (error) {
            console.error('AI function calling failed:', error);
            return null;
        }
    }

    parseAIResponse(response) {
        try {
            // Try to extract JSON from the response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) return null;

            const parsed = JSON.parse(jsonMatch[0]);
            
            if (parsed.function_call && parsed.function && parsed.parameters) {
                return {
                    function: parsed.function,
                    parameters: parsed.parameters,
                    reasoning: parsed.reasoning
                };
            }

            return null;
        } catch (error) {
            console.error('Failed to parse AI response:', error);
            return null;
        }
    }

    async executeFunction(functionName, parameters) {
        console.log(`🔧 Executing function: ${functionName}`, parameters);

        try {
            const result = await this.meenoeIntegration.executeFunction(functionName, parameters);
            
            return {
                success: true,
                type: 'function_call',
                function: functionName,
                parameters: parameters,
                result: result,
                message: this.generateSuccessMessage(functionName, parameters, result)
            };

        } catch (error) {
            console.error(`❌ Function execution failed: ${functionName}`, error);
            
            return {
                success: false,
                type: 'function_error',
                function: functionName,
                parameters: parameters,
                error: error.message,
                message: `I tried to ${functionName} but encountered an error: ${error.message}`
            };
        }
    }

    generateSuccessMessage(functionName, parameters, result) {
        switch (functionName) {
            case 'createAgendaPoint':
                return `✅ Created agenda point "${parameters.title}"${parameters.urgency && parameters.urgency !== 'important' ? ` with ${parameters.urgency} urgency` : ''}`;
            
            case 'createAction':
                return `✅ Created action item "${parameters.title}"${parameters.assignee ? ` assigned to ${parameters.assignee}` : ''}${parameters.priority !== 'medium' ? ` with ${parameters.priority} priority` : ''}`;
            
            case 'updateAgendaPoint':
                return `✅ Updated agenda point "${parameters.title || result.title}"`;
            
            case 'deleteAgendaPoint':
                return `✅ Deleted agenda point`;
            
            default:
                return `✅ Successfully executed ${functionName}`;
        }
    }

    async generateConversationalResponse(userMessage, context) {
        const prompt = `You are a helpful AI assistant for Meenoe meeting management. The user said: "${userMessage}"

Current meeting context:
${JSON.stringify(context, null, 2)}

Provide a helpful, conversational response. If the user seems to want to perform an action but wasn't specific enough, guide them on how to be more specific.`;

        try {
            return await this.aiProvider.generateResponse(prompt, context, {
                temperature: 0.7,
                maxTokens: 300
            });
        } catch (error) {
            return "I'm here to help you manage your meeting. You can ask me to create agenda points, add action items, or analyze your meeting structure.";
        }
    }

    // Utility methods
    getFunctionRegistry() {
        return this.functionRegistry;
    }

    getIntentPatterns() {
        return this.intentPatterns;
    }

    // Debug method to test function calling
    async testFunctionCalling() {
        console.log('🧪 Testing Unified Function Calling System');

        const testCases = [
            'Create a new agenda point named "Budget Review"',
            'Add action item "Review quarterly reports" assigned to John',
            'Create agenda point called "Team Updates" with high priority',
            'Add todo item "Prepare presentation" due 2024-01-15'
        ];

        for (const testCase of testCases) {
            console.log(`\n🧪 Testing: "${testCase}"`);
            try {
                const result = await this.processRequest(testCase, {});
                console.log('✅ Result:', result);
            } catch (error) {
                console.error('❌ Error:', error);
            }
        }
    }
}

// Export for use in other modules
window.UnifiedFunctionCaller = UnifiedFunctionCaller;
